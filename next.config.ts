import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    // Recommended: Restrict hostnames to known domains for security.
    // Using '**' is permissive. Replace with specific hostnames like:
    // { protocol: 'https', hostname: 'cdn.example.com' },
    // { protocol: 'https', hostname: 's3.amazonaws.com' },
    // Only HTTPS is allowed by default below. Add specific HTTP sources only if necessary and trusted.
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // TODO: Replace with specific, trusted hostnames.
      },
    ],
  },
  experimental: {
    typedRoutes: true,
  },
  // Add webpack configuration to help with chunk loading issues
  webpack: (config, { dev, isServer }) => {
    // Improve chunk loading reliability
    if (!dev && !isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            default: {
              minChunks: 1,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              chunks: 'all',
            },
          },
        },
      };
    }
    return config;
  },
  // Add output configuration for better chunk handling
  output: 'standalone',
};

export default nextConfig;
