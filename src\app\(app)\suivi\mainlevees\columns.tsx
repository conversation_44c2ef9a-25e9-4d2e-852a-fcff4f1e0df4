"use client";

import { ColumnDef } from "@tanstack/react-table";
import { <PERSON><PERSON><PERSON>, Garantie, Projet, ClientBeneficiaire, AllocationLignePartenaire, Partenaire, StatutMainlevee, Utilisateur } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowUpDown, Edit } from "lucide-react";

export type MainleveeDemandeColumn = Mainlevee & {
  garantie: Pick<Garantie, "id" | "referenceGarantie"> & {
    projet: Pick<Projet, "id"> & {
      clientBeneficiaire: Pick<ClientBeneficiaire, "id" | "nomOuRaisonSociale">;
    };
    allocation: Pick<AllocationLignePartenaire, "id"> & {
      partenaire: Pick<Partenaire, "id" | "nom">;
    };
  };
  utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur" | "nom" | "prenom"> | null;
};

interface MainleveeDemandeColumnsProps {
    onProcess: (demande: MainleveeDemandeColumn) => void;
    canProcess?: boolean; // Nouveau paramètre pour indiquer si l'utilisateur peut traiter les mainlevées
}

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';

const getStatutMainleveeBadgeVariant = (statut: StatutMainlevee | undefined) => {
    switch (statut) {
        case StatutMainlevee.Demandee: return "bg-blue-100 text-blue-700 border-blue-300";
        case StatutMainlevee.EnCoursApprobation: return "bg-yellow-100 text-yellow-700 border-yellow-300";
        case StatutMainlevee.Accordee: return "bg-green-100 text-green-700 border-green-300";
        case StatutMainlevee.Refusee: return "bg-red-100 text-red-700 border-red-300";
        default: return "bg-slate-100 text-slate-700 border-slate-300";
    }
};

export const getMainleveeDemandeColumns = ({ onProcess, canProcess = true }: MainleveeDemandeColumnsProps): ColumnDef<MainleveeDemandeColumn>[] => [
  {
    accessorKey: "garantie.referenceGarantie",
    header: "Réf. Garantie",
    cell: ({ row }) => row.original.garantie.referenceGarantie,
  },
  {
    accessorKey: "garantie.projet.clientBeneficiaire.nomOuRaisonSociale",
    header: "Client Bénéficiaire",
    cell: ({ row }) => row.original.garantie.projet.clientBeneficiaire.nomOuRaisonSociale,
  },
  {
    accessorKey: "garantie.allocation.partenaire.nom",
    header: "Partenaire",
    cell: ({ row }) => row.original.garantie.allocation.partenaire.nom,
  },
  {
    accessorKey: "typeMainlevee",
    header: "Type Demande",
    cell: ({ row }) => <Badge variant="outline">{row.getValue("typeMainlevee")}</Badge>,
  },
  {
    accessorKey: "dateDemande",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Date Demande <ArrowUpDown className="ml-1 h-3 w-3"/> </Button> ),
    cell: ({ row }) => formatDate(row.getValue("dateDemande")),
  },
  {
    accessorKey: "statut",
    header: "Statut Demande",
    cell: ({ row }) => <Badge className={getStatutMainleveeBadgeVariant(row.getValue("statut"))}>{row.getValue("statut")}</Badge>,
  },
  {
    accessorKey: "utilisateurCreation.nomUtilisateur",
    header: "Demandeur",
    cell: ({ row }) => row.original.utilisateurCreation?.nomUtilisateur || "N/A",
  },
  // Colonne actions seulement si l'utilisateur peut traiter les mainlevées
  ...(canProcess ? [{
    id: "actions",
    cell: ({ row }) => {
      const demande = row.original;
      if (demande.statut === StatutMainlevee.Demandee || demande.statut === StatutMainlevee.EnCoursApprobation) {
        return (
          <Button variant="outline" size="sm" onClick={() => onProcess(demande)}>
            <Edit className="mr-2 h-4 w-4" /> Traiter
          </Button>
        );
      }
      return null;
    },
  }] : []),
];