"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function MisesEnJeuRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirection immédiate vers la vraie page des mises en jeu
    router.replace("/suivi/mises-en-jeu");
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirection vers les mises en jeu...</p>
      </div>
    </div>
  );
}
