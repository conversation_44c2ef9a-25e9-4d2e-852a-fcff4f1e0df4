#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing ChunkLoadError - Clearing Next.js cache and rebuilding...\n');

// Function to remove directory recursively
function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    console.log(`🗑️  Removing ${dirPath}...`);
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`✅ Removed ${dirPath}`);
  } else {
    console.log(`ℹ️  ${dirPath} doesn't exist, skipping...`);
  }
}

try {
  // 1. Remove .next directory
  removeDir('.next');
  
  // 2. Remove node_modules/.cache if it exists
  removeDir('node_modules/.cache');
  
  // 3. Remove any TypeScript build cache
  removeDir('.tsbuildinfo');
  
  // 4. Clear npm cache
  console.log('\n🧹 Clearing npm cache...');
  try {
    execSync('npm cache clean --force', { stdio: 'inherit' });
    console.log('✅ npm cache cleared');
  } catch (error) {
    console.log('⚠️  Could not clear npm cache:', error.message);
  }
  
  // 5. Reinstall dependencies to ensure consistency
  console.log('\n📦 Reinstalling dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies reinstalled');
  } catch (error) {
    console.log('❌ Error reinstalling dependencies:', error.message);
    process.exit(1);
  }
  
  // 6. Generate Prisma client
  console.log('\n🔄 Regenerating Prisma client...');
  try {
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client regenerated');
  } catch (error) {
    console.log('⚠️  Could not regenerate Prisma client:', error.message);
  }
  
  console.log('\n🎉 Cache clearing completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Restart your development server: npm run dev');
  console.log('2. Hard refresh your browser (Ctrl+Shift+R or Cmd+Shift+R)');
  console.log('3. Clear browser cache if the issue persists');
  console.log('\n💡 If ChunkLoadError persists, try:');
  console.log('   - Disable browser extensions');
  console.log('   - Try incognito/private browsing mode');
  console.log('   - Check browser console for additional errors');
  
} catch (error) {
  console.error('❌ Error during cache clearing:', error.message);
  process.exit(1);
}
