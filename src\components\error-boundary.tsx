"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Check if it's a ChunkLoadError
    if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {
      console.log('ChunkLoadError detected, attempting to reload...');
      // Automatically reload the page for chunk errors
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
    
    this.setState({
      error,
      errorInfo,
    });
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const { error } = this.state;
      
      // Check if it's a ChunkLoadError
      if (error?.name === 'ChunkLoadError' || error?.message.includes('Loading chunk')) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-background">
            <div className="max-w-md w-full mx-auto p-6 text-center">
              <div className="mb-4">
                <RefreshCw className="h-12 w-12 mx-auto text-blue-500 animate-spin" />
              </div>
              <h2 className="text-xl font-semibold mb-2">Mise à jour en cours...</h2>
              <p className="text-muted-foreground mb-4">
                L'application se met à jour. Veuillez patienter...
              </p>
              <div className="text-sm text-muted-foreground">
                Rechargement automatique dans quelques secondes
              </div>
            </div>
          </div>
        );
      }

      // Use custom fallback if provided
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={error!} retry={this.retry} />;
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="max-w-md w-full mx-auto p-6 text-center">
            <div className="mb-4">
              <AlertTriangle className="h-12 w-12 mx-auto text-destructive" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Une erreur s'est produite</h2>
            <p className="text-muted-foreground mb-4">
              Désolé, quelque chose s'est mal passé. Veuillez réessayer.
            </p>
            {process.env.NODE_ENV === 'development' && error && (
              <details className="mb-4 text-left">
                <summary className="cursor-pointer text-sm font-medium">
                  Détails de l'erreur (développement)
                </summary>
                <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                  {error.toString()}
                  {error.stack && `\n${error.stack}`}
                </pre>
              </details>
            )}
            <div className="space-y-2">
              <Button onClick={this.retry} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Réessayer
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()} 
                className="w-full"
              >
                Recharger la page
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for handling chunk loading errors in functional components
export function useChunkErrorHandler() {
  React.useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (event.error?.name === 'ChunkLoadError' || 
          event.message?.includes('Loading chunk')) {
        console.log('ChunkLoadError detected in window error handler');
        window.location.reload();
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason?.name === 'ChunkLoadError' || 
          event.reason?.message?.includes('Loading chunk')) {
        console.log('ChunkLoadError detected in unhandled rejection');
        window.location.reload();
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);
}

export default ErrorBoundary;
