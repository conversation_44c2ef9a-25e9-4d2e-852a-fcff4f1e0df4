"use client";
// src/app/(app)/suivi/mises-en-jeu/page.tsx
// "use client"; // SUPPRIMÉ

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams, usePathname } from "next/navigation"; // Pour les filtres URL
import { StatutMiseEnJeu } from "@/types/enums";

import { getMiseEnJeuDemandeColumns, MiseEnJeuDemandeColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { DecisionMiseEnJeuFormValues, DecisionMiseEnJeuSchema, PaiementMiseEnJeuFormValues, PaiementMiseEnJeuSchema } from "@/lib/schemas/mise-en-jeu.schema"; // Importer les schémas
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import PaginationControls from "@/components/shared/pagination-controls";

const formatCurrency = (amount: any, currency: string = "XOF") => {
  if (amount === null || amount === undefined) return "-";
  const num = typeof amount === 'string' ? parseFloat(amount.replace(',', '.')) : amount;
  if (isNaN(num)) return "-";
  return new Intl.NumberFormat("fr-FR", { style: "currency", currency: currency, minimumFractionDigits: 0 }).format(num);
};

// Options pour le Select de décision (limité aux statuts de décision)
const decisionStatutOptionsMiseEnJeu = [
    { value: StatutMiseEnJeu.ApprouveeTotalement, label: "Approuvée Totalement" },
    { value: StatutMiseEnJeu.ApprouveePartiellement, label: "Approuvée Partiellement" },
    { value: StatutMiseEnJeu.Refusee, label: "Refusée" },
    { value: StatutMiseEnJeu.EnCoursInstruction, label: "Mettre en Cours d'Instruction" }, // Option pour changer le statut
];

interface MiseEnJeuApiResponse {
    data: MiseEnJeuDemandeColumn[];
    totalPages: number;
    currentPage: number;
    totalRecords: number;
}

export default function MisesEnJeuEnAttentePage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const searchParamsHook = useSearchParams(); // Renommé pour éviter conflit
  const { toast } = useToast();

  const [demandes, setDemandes] = useState<MiseEnJeuDemandeColumn[]>([]);
  const [pageStatus, setPageStatus] = useState<"loading" | "loaded" | "error" | "unauthorized">("loading");
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);


  const [isDecisionFormOpen, setIsDecisionFormOpen] = useState(false);
  const [processingDemande, setProcessingDemande] = useState<MiseEnJeuDemandeColumn | null>(null);

  // États pour le formulaire de paiement
  const [isPaiementFormOpen, setIsPaiementFormOpen] = useState(false);
  const [paiementDemande, setPaiementDemande] = useState<MiseEnJeuDemandeColumn | null>(null);

  const decisionForm = useForm<DecisionMiseEnJeuFormValues>({
    resolver: zodResolver(DecisionMiseEnJeuSchema),
    defaultValues: {
      statut: undefined,
      dateDecision: new Date(),
      montantApprouveStr: "",
      commentairesDecision: "",
    },
  });
  const decisionStatutSelected = decisionForm.watch("statut");

  // Formulaire de paiement
  const paiementForm = useForm<PaiementMiseEnJeuFormValues>({
    resolver: zodResolver(PaiementMiseEnJeuSchema),
    defaultValues: {
      montantPayeStr: "",
      datePaiement: new Date(),
      referencePaiement: "",
    },
  });

  const fetchDemandesMiseEnJeu = useCallback(async (page = 1, searchParams?: URLSearchParams) => {
    if (pageStatus !== "loading" && !isLoadingInternal.current) isLoadingInternal.current = true;
    const currentParams = searchParams || new URLSearchParams(searchParamsHook.toString());
    currentParams.set("page", page.toString());
    currentParams.set("limit", "15");

    // router.replace(`${pathname}?${currentParams.toString()}`, { scroll: false }); // Mettre à jour l'URL

    try {
      const response = await fetch(`/api/mises-en-jeu?${currentParams.toString()}`);
      if (!response.ok) throw new Error("Échec de la récupération des demandes de mise en jeu");
      const result: MiseEnJeuApiResponse = await response.json();
      setDemandes(result.data);
      setTotalPages(result.totalPages);
      setCurrentPage(result.currentPage);
      setTotalRecords(result.totalRecords);
      if (pageStatus === "loading") setPageStatus("loaded");
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      if (pageStatus === "loading") setPageStatus("error");
    } finally {
        if (pageStatus === "loading" || isLoadingInternal.current) isLoadingInternal.current = false;
    }
  }, []); // Supprimer toutes les dépendances pour éviter la boucle infinie

  // Utiliser un ref pour gérer le chargement interne pour éviter les boucles avec setIsLoading
  const isLoadingInternal = React.useRef(false);


  useEffect(() => {
    if (sessionStatus === "loading") { setPageStatus("loading"); return; }
    if (
      !session ||
      !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Auditeur", "Partenaire"].includes(session.user?.role as string)
    ) {
      if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/"); setPageStatus("unauthorized"); return;
    }
    if (session && (pageStatus === "loading" || pageStatus === "error")) {
        const pageFromUrl = parseInt(searchParamsHook.get("page") || "1");
        fetchDemandesMiseEnJeu(pageFromUrl, new URLSearchParams(searchParamsHook.toString()));
    }
  }, [session, sessionStatus, pageStatus, router, searchParamsHook]); // Supprimer toast et fetchDemandesMiseEnJeu des dépendances


  const handleProcessDemande = useCallback((demande: MiseEnJeuDemandeColumn) => {
    setProcessingDemande(demande);
    decisionForm.reset({
      statut: demande.statut === StatutMiseEnJeu.Demandee || demande.statut === StatutMiseEnJeu.EnCoursInstruction ? undefined : demande.statut,
      dateDecision: demande.dateDecision ? new Date(demande.dateDecision) : new Date(),
      montantApprouveStr: demande.montantApprouve?.toString().replace('.',',') || demande.montantDemande.toString().replace('.',','), // Pré-remplir avec montant demandé
      commentairesDecision: demande.commentairesDecision || "",
    });
    setIsDecisionFormOpen(true);
  }, [decisionForm]);

  const onDecisionSubmit = useCallback(async (values: DecisionMiseEnJeuFormValues) => {
    if (!processingDemande) return;
    // S'assurer que montantApprouveStr est fourni si statut est ApprouveeTotalement ou Partiellement
    if ((values.statut === StatutMiseEnJeu.ApprouveeTotalement || values.statut === StatutMiseEnJeu.ApprouveePartiellement) && (!values.montantApprouveStr || values.montantApprouveStr.trim() === "")) {
        decisionForm.setError("montantApprouveStr", { type: "manual", message: "Le montant approuvé est requis." });
        return;
    }
    // Si ApprouveeTotalement, le montant approuvé doit être égal au montant demandé
    if (values.statut === StatutMiseEnJeu.ApprouveeTotalement) {
        const montantDemandeNum = Number((processingDemande.montantDemande as any)?.toString?.() ?? processingDemande.montantDemande);
        const montantApprouve = parseFloat(values.montantApprouveStr!.replace(',', '.'));
        if (montantApprouve !== montantDemandeNum) {
            decisionForm.setError("montantApprouveStr", { type: "manual", message: `Doit être égal au montant demandé (${formatCurrency(montantDemandeNum, processingDemande.garantie.allocation.ligneGarantie.devise)}).` });
            return;
        }
    }


    try {
      // L'API pour la décision sera /api/mises-en-jeu/[id]/decision (ou /api/mises-en-jeu/[id] avec méthode PUT)
      const response = await fetch(`/api/mises-en-jeu/${processingDemande.id}/decision`, {
        method: "PUT", // ou POST si vous préférez pour une action "decision"
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const responseData = await response.json();
      if (!response.ok) {
        if (responseData.errors) {
            Object.entries(responseData.errors).forEach(([key, value]) => {
                decisionForm.setError(key as keyof DecisionMiseEnJeuFormValues, { type: 'server', message: (value as string[]).join(', ') });
            });
        }
        throw new Error(responseData.message || "Échec du traitement de la demande de mise en jeu");
      }
      toast({ title: "Décision Enregistrée", description: `La demande de mise en jeu pour ${processingDemande.garantie.referenceGarantie} a été traitée.` });
      setIsDecisionFormOpen(false);
      setProcessingDemande(null);
      fetchDemandesMiseEnJeu(currentPage, new URLSearchParams(searchParamsHook.toString())); // Recharger la liste
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [processingDemande, decisionForm, currentPage, searchParamsHook]); // Supprimer toast et fetchDemandesMiseEnJeu

  // Fonction pour gérer l'ouverture du formulaire de paiement
  const handlePaiementDemande = useCallback((demande: MiseEnJeuDemandeColumn) => {
    // Vérifier que l'utilisateur a les droits pour enregistrer un paiement
    if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
      toast({ title: "Accès refusé", description: "Vous n'avez pas les droits pour enregistrer un paiement.", variant: "destructive" });
      return;
    }

    setPaiementDemande(demande);
    paiementForm.reset({
      montantPayeStr: demande.montantApprouve?.toString().replace('.', ',') || "",
      datePaiement: new Date(),
      referencePaiement: "",
    });
    setIsPaiementFormOpen(true);
  }, [paiementForm, session]); // Supprimer toast

  // Fonction pour soumettre le paiement
  const onPaiementSubmit = useCallback(async (values: PaiementMiseEnJeuFormValues) => {
    if (!paiementDemande) return;

    try {
      const response = await fetch(`/api/mises-en-jeu/${paiementDemande.id}/paiement`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });

      const responseData = await response.json();
      if (!response.ok) {
        if (responseData.errors) {
          Object.entries(responseData.errors).forEach(([key, value]) => {
            paiementForm.setError(key as keyof PaiementMiseEnJeuFormValues, {
              type: 'server',
              message: (value as string[]).join(', ')
            });
          });
        }
        throw new Error(responseData.message || "Échec de l'enregistrement du paiement");
      }

      toast({
        title: "Paiement Enregistré",
        description: `Le paiement pour la mise en jeu ${paiementDemande.garantie.referenceGarantie} a été enregistré avec succès.`
      });

      setIsPaiementFormOpen(false);
      setPaiementDemande(null);
      fetchDemandesMiseEnJeu(currentPage, new URLSearchParams(searchParamsHook.toString())); // Recharger la liste
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [paiementDemande, paiementForm, currentPage, searchParamsHook]); // Supprimer toast et fetchDemandesMiseEnJeu

  // Seuls les Administrateur et GestionnaireGesGar peuvent traiter les mises en jeu
  const canProcessMisesEnJeu = session?.user?.role === "Administrateur" || session?.user?.role === "GestionnaireGesGar";
  const columns = useMemo(() => getMiseEnJeuDemandeColumns({
    onProcess: handleProcessDemande,
    onPayment: session && ["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
      ? handlePaiementDemande
      : undefined,
    canProcess: canProcessMisesEnJeu
  }), [handleProcessDemande, handlePaiementDemande, session, canProcessMisesEnJeu]);

  // ... (rendu conditionnel basé sur pageStatus) ...
  if (pageStatus === "loading" && !demandes.length) return <div className="p-6 text-center">Chargement des demandes de mise en jeu...</div>;


  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <h1 className="text-3xl font-bold mb-6">Demandes de Mise en Jeu</h1>
      {/* TODO: Ajouter des filtres ici (statut, partenaire, etc.) */}
      <DataTable columns={columns} data={demandes} />
      <PaginationControls currentPage={currentPage} totalPages={totalPages} onPageChange={(page) => fetchDemandesMiseEnJeu(page, new URLSearchParams(searchParamsHook.toString()))} totalRecords={totalRecords}/>

      {processingDemande && (
        <Dialog open={isDecisionFormOpen} onOpenChange={(open) => {
            if (!open) setProcessingDemande(null);
            setIsDecisionFormOpen(open);
        }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Traiter la Demande de Mise en Jeu</DialogTitle>
              <DialogDescription>
                Garantie: <strong>{processingDemande.garantie.referenceGarantie}</strong> <br/>
                Partenaire: {processingDemande.garantie.allocation.partenaire.nom} <br/>
                Montant Demandé: {formatCurrency(processingDemande.montantDemande, processingDemande.garantie.allocation.ligneGarantie.devise)}
              </DialogDescription>
            </DialogHeader>
            <Form {...decisionForm}>
              <form onSubmit={decisionForm.handleSubmit(onDecisionSubmit)} className="space-y-4 py-4">
                <FormField control={decisionForm.control} name="statut" render={({ field }) => (
                    <FormItem><FormLabel>Décision <span className="text-red-500">*</span></FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir une décision..." /></SelectTrigger></FormControl>
                            <SelectContent>{decisionStatutOptionsMiseEnJeu.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                <FormField control={decisionForm.control} name="dateDecision" render={({ field }) => (
                    <FormItem className="flex flex-col"><FormLabel>Date de Décision <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value} onDateChange={field.onChange} /><FormMessage /></FormItem>
                )} />
                {(decisionStatutSelected === StatutMiseEnJeu.ApprouveeTotalement || decisionStatutSelected === StatutMiseEnJeu.ApprouveePartiellement) && (
                    <FormField control={decisionForm.control} name="montantApprouveStr" render={({ field }) => (
                        <FormItem><FormLabel>Montant Approuvé <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Montant que le Fonds accepte de payer" {...field} /></FormControl><FormMessage /></FormItem>
                    )} />
                )}
                <FormField control={decisionForm.control} name="commentairesDecision" render={({ field }) => (
                    <FormItem><FormLabel>Commentaires sur la Décision</FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <DialogFooter className="pt-4">
                    <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                    <Button type="submit" disabled={decisionForm.formState.isSubmitting}>
                        {decisionForm.formState.isSubmitting ? "Enregistrement..." : "Enregistrer la Décision"}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}

      {/* Dialog de Paiement */}
      {paiementDemande && (
        <Dialog open={isPaiementFormOpen} onOpenChange={(open) => {
            if (!open) setPaiementDemande(null);
            setIsPaiementFormOpen(open);
        }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Enregistrer le Paiement de la Mise en Jeu</DialogTitle>
              <DialogDescription>
                Garantie: <strong>{paiementDemande.garantie.referenceGarantie}</strong> <br/>
                Partenaire: {paiementDemande.garantie.allocation.partenaire.nom} <br/>
                Montant Approuvé: {formatCurrency(paiementDemande.montantApprouve, paiementDemande.garantie.allocation.ligneGarantie.devise)}
              </DialogDescription>
            </DialogHeader>
            <Form {...paiementForm}>
              <form onSubmit={paiementForm.handleSubmit(onPaiementSubmit)} className="space-y-4 py-4">
                <FormField control={paiementForm.control} name="montantPayeStr" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Montant Payé <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Montant effectivement payé"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                )} />

                <FormField control={paiementForm.control} name="datePaiement" render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Date de Paiement <span className="text-red-500">*</span></FormLabel>
                      <DatePicker date={field.value} onDateChange={field.onChange} />
                      <FormMessage />
                    </FormItem>
                )} />

                <FormField control={paiementForm.control} name="referencePaiement" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Référence du Paiement <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Numéro de référence du paiement"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                )} />

                <DialogFooter className="pt-4">
                    <DialogClose asChild>
                      <Button type="button" variant="outline">Annuler</Button>
                    </DialogClose>
                    <Button type="submit" disabled={paiementForm.formState.isSubmitting}>
                        {paiementForm.formState.isSubmitting ? "Enregistrement..." : "Enregistrer le Paiement"}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}