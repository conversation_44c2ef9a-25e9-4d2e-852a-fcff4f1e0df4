// src/app/providers.tsx
"use client"; // Ce composant doit être un Client Component

import { SessionProvider } from "next-auth/react";
import React from "react";
import { useChunkErrorHandler } from "@/components/error-boundary";

interface ProvidersProps {
  children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  // Handle chunk loading errors
  useChunkErrorHandler();

  return <SessionProvider>{children}</SessionProvider>;
}
